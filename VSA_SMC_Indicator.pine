//@version=5
indicator("VSA-SMC Professional Indicator", shorttitle="VSA-SMC", overlay=true, max_bars_back=500)

// ═══════════════════════════════════════════════════════════════════════════════════════
// INPUT PARAMETERS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Volume Analysis Settings
volumeGroup = "Volume Analysis"
volumePeriod = input.int(20, "Volume Analysis Period", minval=5, maxval=100, group=volumeGroup)
highVolumeThreshold = input.float(1.5, "High Volume Threshold", minval=1.0, maxval=3.0, step=0.1, group=volumeGroup)
lowVolumeThreshold = input.float(0.7, "Low Volume Threshold", minval=0.1, maxval=1.0, step=0.1, group=volumeGroup)

// Spread Analysis Settings
spreadGroup = "Spread Analysis"
spreadPeriod = input.int(20, "Spread Analysis Period", minval=5, maxval=100, group=spreadGroup)
wideSpreadThreshold = input.float(1.3, "Wide Spread Threshold", minval=1.0, maxval=2.0, step=0.1, group=spreadGroup)
narrowSpreadThreshold = input.float(0.8, "Narrow Spread Threshold", minval=0.1, maxval=1.0, step=0.1, group=spreadGroup)

// Signal Configuration
signalGroup = "Signal Configuration"
requiredConfirmations = input.int(2, "Required Confirmations", minval=1, maxval=3, group=signalGroup)
signalSensitivity = input.string("Medium", "Signal Sensitivity", options=["Low", "Medium", "High"], group=signalGroup)
atrPeriod = input.int(14, "ATR Period for Volatility", minval=5, maxval=50, group=signalGroup)

// Visual Settings
visualGroup = "Visual Settings"
showPhaseBackground = input.bool(true, "Show Market Phase Background", group=visualGroup)
showPatternLabels = input.bool(true, "Show VSA Pattern Labels", group=visualGroup)
showVolumeHistogram = input.bool(true, "Show Volume Histogram", group=visualGroup)
showSignalArrows = input.bool(true, "Show Signal Arrows", group=visualGroup)

// Alert Settings
alertGroup = "Alert Settings"
enableAlerts = input.bool(true, "Enable Alerts", group=alertGroup)
alertOnHighConfidence = input.bool(true, "Alert Only High Confidence Signals", group=alertGroup)

// Color Settings
colorGroup = "Color Settings"
accumulationColor = input.color(color.new(color.green, 90), "Accumulation Phase", group=colorGroup)
markupColor = input.color(color.new(color.blue, 90), "Markup Phase", group=colorGroup)
distributionColor = input.color(color.new(color.red, 90), "Distribution Phase", group=colorGroup)
testColor = input.color(color.new(color.yellow, 90), "Test Phase", group=colorGroup)
buySignalColor = input.color(color.green, "Buy Signal Color", group=colorGroup)
sellSignalColor = input.color(color.red, "Sell Signal Color", group=colorGroup)

// ═══════════════════════════════════════════════════════════════════════════════════════
// CORE VSA CALCULATION FUNCTIONS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Volume Analysis Functions
calculateRelativeVolume() =>
    avgVolume = ta.sma(volume, volumePeriod)
    relVol = volume / avgVolume
    relVol

getVolumeClassification(relativeVolume) =>
    if relativeVolume > highVolumeThreshold
        "High"
    else if relativeVolume < lowVolumeThreshold
        "Low"
    else
        "Normal"

// Spread Analysis Functions
calculateSpread() =>
    spread = high - low
    avgSpread = ta.sma(spread, spreadPeriod)
    relativeSpread = spread / avgSpread
    [spread, avgSpread, relativeSpread]

getSpreadClassification(relativeSpread) =>
    if relativeSpread > wideSpreadThreshold
        "Wide"
    else if relativeSpread < narrowSpreadThreshold
        "Narrow"
    else
        "Normal"

// Close Position Analysis
calculateClosePosition() =>
    barRange = high - low
    if barRange > 0
        closePos = (close - low) / barRange
        closePos
    else
        0.5  // Neutral position for doji bars

getClosePositionClassification(closePosition) =>
    if closePosition > 0.7
        "Strong"
    else if closePosition < 0.3
        "Weak"
    else
        "Neutral"

// Effort vs Result Analysis
analyzeEffortVsResult(relativeVolume, relativeSpread, priceChange) =>
    effort = relativeVolume * relativeSpread
    result = math.abs(priceChange) / ta.atr(atrPeriod)
    
    if effort > 1.2 and result < 0.5
        "High Effort, Low Result"
    else if effort < 0.8 and result > 1.2
        "Low Effort, High Result"
    else
        "Balanced"

// ═══════════════════════════════════════════════════════════════════════════════════════
// VSA PATTERN DETECTION FUNCTIONS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Buying Climax Detection
detectBuyingClimax(relativeVolume, relativeSpread, closePosition) =>
    highVolume = relativeVolume > highVolumeThreshold
    wideSpread = relativeSpread > wideSpreadThreshold
    weakClose = closePosition < 0.3
    upBar = close > open
    
    buyingClimax = highVolume and wideSpread and weakClose and upBar
    buyingClimax

// Selling Climax Detection
detectSellingClimax(relativeVolume, relativeSpread, closePosition) =>
    highVolume = relativeVolume > highVolumeThreshold
    wideSpread = relativeSpread > wideSpreadThreshold
    strongClose = closePosition > 0.7
    downBar = close < open
    
    sellingClimax = highVolume and wideSpread and strongClose and downBar
    sellingClimax

// No Demand Detection
detectNoDemand(relativeVolume, relativeSpread) =>
    lowVolume = relativeVolume < lowVolumeThreshold
    narrowSpread = relativeSpread < narrowSpreadThreshold
    upBar = close > open
    
    noDemand = lowVolume and narrowSpread and upBar
    noDemand

// Stopping Volume Detection
detectStoppingVolume(relativeVolume, relativeSpread) =>
    highVolume = relativeVolume > highVolumeThreshold
    narrowSpread = relativeSpread < narrowSpreadThreshold
    downBar = close < open
    
    stoppingVolume = highVolume and narrowSpread and downBar
    stoppingVolume

// Upthrust Detection
detectUpthrust(relativeVolume) =>
    // Check if current bar made new high but closed lower
    newHigh = high > high[1]
    closedLower = close < high
    moderateVolume = relativeVolume > 1.0
    previousUpBar = close[1] > open[1]
    
    upthrust = newHigh and closedLower and moderateVolume and previousUpBar
    upthrust

// Spring Detection
detectSpring(relativeVolume) =>
    // Check if current bar made new low but closed higher
    newLow = low < low[1]
    closedHigher = close > low
    normalOrLowVolume = relativeVolume <= 1.1
    previousDownBar = close[1] < open[1]
    
    spring = newLow and closedHigher and normalOrLowVolume and previousDownBar
    spring

// ═══════════════════════════════════════════════════════════════════════════════════════
// MARKET PHASE IDENTIFICATION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Trend Analysis for Phase Detection
getTrendDirection() =>
    ema20 = ta.ema(close, 20)
    ema50 = ta.ema(close, 50)
    
    if ema20 > ema50 and close > ema20
        "Uptrend"
    else if ema20 < ema50 and close < ema20
        "Downtrend"
    else
        "Sideways"

// Market Phase Detection
identifyMarketPhase(relativeVolume, relativeSpread, trendDirection) =>
    avgVolume = ta.sma(relativeVolume, 10)
    avgSpread = ta.sma(relativeSpread, 10)
    
    // Accumulation: Sideways + Decreasing volume + Narrow spreads
    if trendDirection == "Sideways" and avgVolume < 1.0 and avgSpread < 1.0
        "Accumulation"
    // Markup: Uptrend + Increasing volume on up-moves + Normal/Wide spreads
    else if trendDirection == "Uptrend" and relativeVolume > 1.0 and close > open
        "Markup"
    // Distribution: At highs + High volume + Little progress
    else if close >= ta.highest(close, 20) * 0.98 and avgVolume > 1.2 and avgSpread > 1.0
        "Distribution"
    // Test: Retesting levels + Reduced volume
    else if avgVolume < 0.9 and avgSpread < 0.9
        "Test"
    else
        "Transition"

// ═══════════════════════════════════════════════════════════════════════════════════════
// SIGNAL GENERATION SYSTEM
// ═══════════════════════════════════════════════════════════════════════════════════════

// Calculate all VSA components
relativeVolume = calculateRelativeVolume()
[spread, avgSpread, relativeSpread] = calculateSpread()
closePosition = calculateClosePosition()
priceChange = close - open
effortVsResult = analyzeEffortVsResult(relativeVolume, relativeSpread, priceChange)
trendDirection = getTrendDirection()
marketPhase = identifyMarketPhase(relativeVolume, relativeSpread, trendDirection)

// Pattern Detection
buyingClimax = detectBuyingClimax(relativeVolume, relativeSpread, closePosition)
sellingClimax = detectSellingClimax(relativeVolume, relativeSpread, closePosition)
noDemand = detectNoDemand(relativeVolume, relativeSpread)
stoppingVolume = detectStoppingVolume(relativeVolume, relativeSpread)
upthrust = detectUpthrust(relativeVolume)
spring = detectSpring(relativeVolume)

// Signal Generation Logic
generateBuySignal() =>
    confirmations = 0
    
    // Primary buy signals
    if sellingClimax
        confirmations += 2
    if spring
        confirmations += 2
    if stoppingVolume
        confirmations += 1
    
    // Phase confirmation
    if marketPhase == "Accumulation" or marketPhase == "Test"
        confirmations += 1
    
    // Trend confirmation
    if trendDirection != "Downtrend"
        confirmations += 1
    
    buySignal = confirmations >= requiredConfirmations
    confidence = confirmations >= 3 ? "High" : confirmations >= 2 ? "Medium" : "Low"
    
    [buySignal, confidence]

generateSellSignal() =>
    confirmations = 0
    
    // Primary sell signals
    if buyingClimax
        confirmations += 2
    if upthrust
        confirmations += 2
    if noDemand
        confirmations += 1
    
    // Phase confirmation
    if marketPhase == "Distribution"
        confirmations += 1
    
    // Trend confirmation
    if trendDirection != "Uptrend"
        confirmations += 1
    
    sellSignal = confirmations >= requiredConfirmations
    confidence = confirmations >= 3 ? "High" : confirmations >= 2 ? "Medium" : "Low"
    
    [sellSignal, confidence]

// Generate signals
[buySignal, buyConfidence] = generateBuySignal()
[sellSignal, sellConfidence] = generateSellSignal()

// ═══════════════════════════════════════════════════════════════════════════════════════
// VISUAL ELEMENTS AND PLOTTING
// ═══════════════════════════════════════════════════════════════════════════════════════

// Market Phase Background
phaseColor = switch marketPhase
    "Accumulation" => accumulationColor
    "Markup" => markupColor
    "Distribution" => distributionColor
    "Test" => testColor
    => color.new(color.gray, 95)

if showPhaseBackground
    bgcolor(phaseColor, title="Market Phase Background")

// Signal Arrows
if showSignalArrows and buySignal
    label.new(bar_index, low - ta.atr(14) * 0.5, "BUY\n" + buyConfidence, 
              color=buySignalColor, style=label.style_label_up, size=size.normal)

if showSignalArrows and sellSignal
    label.new(bar_index, high + ta.atr(14) * 0.5, "SELL\n" + sellConfidence, 
              color=sellSignalColor, style=label.style_label_down, size=size.normal)

// Pattern Labels
if showPatternLabels
    if buyingClimax
        label.new(bar_index, high, "BC", color=color.red, style=label.style_label_down, size=size.small)
    if sellingClimax
        label.new(bar_index, low, "SC", color=color.green, style=label.style_label_up, size=size.small)
    if noDemand
        label.new(bar_index, high, "ND", color=color.orange, style=label.style_label_down, size=size.small)
    if stoppingVolume
        label.new(bar_index, low, "SV", color=color.blue, style=label.style_label_up, size=size.small)
    if upthrust
        label.new(bar_index, high, "UT", color=color.purple, style=label.style_label_down, size=size.small)
    if spring
        label.new(bar_index, low, "SP", color=color.aqua, style=label.style_label_up, size=size.small)

// Volume Histogram
if showVolumeHistogram
    volumeColor = relativeVolume > highVolumeThreshold ? color.red : 
                  relativeVolume < lowVolumeThreshold ? color.orange : color.gray
    plot(relativeVolume, "Relative Volume", color=volumeColor, style=plot.style_histogram, linewidth=2)

// ═══════════════════════════════════════════════════════════════════════════════════════
// ALERTS
// ═══════════════════════════════════════════════════════════════════════════════════════

if enableAlerts
    if buySignal and (not alertOnHighConfidence or buyConfidence == "High")
        alert("VSA-SMC BUY Signal (" + buyConfidence + " confidence) - " + marketPhase + " phase", alert.freq_once_per_bar)
    
    if sellSignal and (not alertOnHighConfidence or sellConfidence == "High")
        alert("VSA-SMC SELL Signal (" + sellConfidence + " confidence) - " + marketPhase + " phase", alert.freq_once_per_bar)

// ═══════════════════════════════════════════════════════════════════════════════════════
// TABLE DISPLAY (Optional Information Panel)
// ═══════════════════════════════════════════════════════════════════════════════════════

if barstate.islast
    var table infoTable = table.new(position.top_right, 2, 6, bgcolor=color.white, border_width=1)
    
    table.cell(infoTable, 0, 0, "Market Phase:", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 0, marketPhase, text_color=color.black, text_size=size.small)
    
    table.cell(infoTable, 0, 1, "Volume:", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 1, getVolumeClassification(relativeVolume), text_color=color.black, text_size=size.small)
    
    table.cell(infoTable, 0, 2, "Spread:", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 2, getSpreadClassification(relativeSpread), text_color=color.black, text_size=size.small)
    
    table.cell(infoTable, 0, 3, "Close Position:", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 3, getClosePositionClassification(closePosition), text_color=color.black, text_size=size.small)
    
    table.cell(infoTable, 0, 4, "Trend:", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 4, trendDirection, text_color=color.black, text_size=size.small)
    
    table.cell(infoTable, 0, 5, "Effort vs Result:", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 5, effortVsResult, text_color=color.black, text_size=size.small)
